<template>
  <payments-page :type="'payouts'" :page="page"></payments-page>
</template>

<script>
import PaymentsPage from '~/components/payments/PaymentsPage'

export default {
  name: 'PayoutsPageWithPagination',
  components: { PaymentsPage },
  middleware: ['authenticated', 'paymentsPageClass'],

  async asyncData({ params, store, query }) {
    const page = parseInt(params.page) || 1
    const searchQuery = query?.search

    await store.dispatch('payments/fetchPayouts', {
      page,
      itemsPerPage: 5, // Set to 5 items per page
    })

    // Set current page in store
    store.commit('payments/SET_CURRENT_PAGE', page)

    return {
      page,
      type: 'payouts',
      searchQuery,
    }
  },

  watchQuery: ['page', 'search'],
}
</script>
