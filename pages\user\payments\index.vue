<template>
  <payments-page :type="'lessons'" :page="1"></payments-page>
</template>

<script>
import PaymentsPage from '~/components/payments/PaymentsPage'

export default {
  name: 'PaymentsLessons',
  components: { PaymentsPage },
  middleware: ['authenticated', 'paymentsPageClass'],
  async asyncData({ store, query }) {
    // Always fetch data on initial load
    await Promise.all([
      store.dispatch('payments/fetchLessons', {
        page: 1,
        itemsPerPage: 5, // Set to 5 items per page
      }),
      store.dispatch('payments/fetchEarningsBalance'),
    ])

    const type = 'lessons'
    const searchQuery = query?.search

    return { type, searchQuery }
  },
}
</script>
