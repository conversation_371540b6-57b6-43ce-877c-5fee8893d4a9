<template>
  <payments-page :type="'lessons'" :page="page"></payments-page>
</template>

<script>
import PaymentsPage from '~/components/payments/PaymentsPage'

export default {
  name: 'PaymentsLessons',
  components: { PaymentsPage },
  middleware: ['authenticated', 'paymentsPageClass'],

  async asyncData({ store, query }) {
    const page = parseInt(query.page) || 1
    // Always fetch data for the current page
    await Promise.all([
      store.dispatch('payments/fetchLessons', {
        page,
        itemsPerPage: 5,
      }),
      store.dispatch('payments/fetchEarningsBalance'),
    ])

    // Set current page in store
    store.commit('payments/SET_CURRENT_PAGE', page)

    return {
      type: 'lessons',
      searchQuery: query?.search,
      page,
    }
  },

  watchQuery: ['page', 'search'],
}
</script>
