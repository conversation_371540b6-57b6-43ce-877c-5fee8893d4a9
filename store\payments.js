export const state = () => ({
  lessons: [],
  payouts: [],
  loading: false,
  error: null,
  totalItems: 0,
  totalLessons: 0,
  totalPayouts: 0,
  currentPage: 1,
  itemsPerPage: 5, // Changed to 5 items per page
  earningsBalance: {
    balance: '0',
    payouts: '0',
    fees: '0',
    futureIncome: '0',
    currencyId: '2',
  },
  payoutFormData: {
    saveUserBankAccounts: [],
    lastPayoutDate: null,
  },
})

export const mutations = {
  SET_LESSONS(state, lessons) {
    state.lessons = lessons
  },
  SET_PAYOUTS(state, payouts) {
    state.payouts = payouts
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_ERROR(state, error) {
    state.error = error
  },
  SET_TOTAL_ITEMS(state, total) {
    state.totalItems = total
  },
  SET_TOTAL_LESSONS(state, total) {
    state.totalLessons = total
  },
  SET_TOTAL_PAYOUTS(state, total) {
    state.totalPayouts = total
  },
  SET_CURRENT_PAGE(state, page) {
    state.currentPage = page
  },
  SET_EARNINGS_BALANCE(state, data) {
    state.earningsBalance = data
  },
  SET_PAYOUT_FORM_DATA(state, data) {
    state.payoutFormData = data
  },
}

export const actions = {
  async fetchLessons({ commit, state }, params = {}) {
    try {
      const page = params.page || 1
      const itemsPerPage = params.itemsPerPage || state.itemsPerPage
      commit('SET_LOADING', true)

      // Add pagination parameters to the API request
      const response = await this.$axios.get(
        `${process.env.NUXT_ENV_API_URL}/users/payments/earnings/${page}/${itemsPerPage}`,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      const data = JSON.parse(response.data)

      // Transform the transactions data
      const transformedLessons = data.transactions.map((transaction) => ({
        id: transaction.transactionId,
        day: new Date(
          transaction.lessonStartDate || transaction.createdAt
        ).toLocaleDateString('en-US', {
          weekday: 'long',
        }),
        date: new Date(transaction.lessonStartDate || transaction.createdAt)
          .toISOString()
          .split('T')[0],
        time: new Date(
          transaction.lessonStartDate || transaction.createdAt
        ).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
        }),
        value: transaction.transactionAmount,
        status: 'completed',
        student: transaction.fullName.replace(/\n/g, ' ').trim(),
        lessonType: `${transaction.lessonOrdinal} of ${transaction.numberOfLessons}`,
        invoiceNo: transaction.invoiceNumber,
        lessonNo: transaction.lessonId || '-',
        finishedAt: new Date(
          transaction.lessonFinishDate || transaction.createdAt
        ).toLocaleString('en-US', {
          day: 'numeric',
          month: 'short',
          hour: '2-digit',
          minute: '2-digit',
        }),
      }))

      commit('SET_LESSONS', transformedLessons)

      // Set the total number of lessons for pagination
      const totalLessons = data.totalItems || transformedLessons.length * 3 // Fallback to ensure pagination works
      commit('SET_TOTAL_LESSONS', totalLessons)
      commit('SET_TOTAL_ITEMS', totalLessons) // For backward compatibility
      commit('SET_CURRENT_PAGE', page)
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchPayouts({ commit, state }, params = {}) {
    try {
      commit('SET_LOADING', true)
      const page = params.page || 1
      const itemsPerPage = params.itemsPerPage || state.itemsPerPage

      const response = await this.$axios.get(
        `${process.env.NUXT_ENV_API_URL}/users/payments/payouts/${page}/${itemsPerPage}`,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      const data = JSON.parse(response.data)

      // Transform the transactions data
      const transformedPayouts = data.transactions.map((transaction) => ({
        id: transaction.invoiceNumber,
        date: new Date(transaction.createdAt).toISOString().split('T')[0],
        time: new Date(transaction.createdAt).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
        }),
        amount: transaction.amount,
        currency: transaction.currency,
        status: 'completed',
        invoiceNo: transaction.invoiceNumber,
        counterPartyType: transaction.counterPartyType,
      }))

      commit('SET_PAYOUTS', transformedPayouts)

      // Set the total number of payouts for pagination
      const totalPayouts = data.totalItems || transformedPayouts.length * 3 // Fallback to ensure pagination works
      commit('SET_TOTAL_PAYOUTS', totalPayouts)
      commit('SET_TOTAL_ITEMS', totalPayouts) // For backward compatibility
      commit('SET_CURRENT_PAGE', page)
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async requestPayout({ commit }, amount) {
    try {
      commit('SET_LOADING', true)
      await this.$axios.post(
        `${process.env.NUXT_ENV_API_URL}/payments/payout`,
        JSON.stringify({ amount }),
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      // Refresh payouts after successful request
      await this.dispatch('payments/fetchPayouts')
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchEarningsBalance({ commit }) {
    try {
      commit('SET_LOADING', true)
      const response = await this.$axios.get(
        `${process.env.NUXT_ENV_API_URL}/users/payments/earnings-balance`,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      const data = JSON.parse(response.data)
      commit('SET_EARNINGS_BALANCE', data)
      return data
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async fetchPayoutFormData({ commit }) {
    try {
      commit('SET_LOADING', true)
      const response = await this.$axios.get(
        `${process.env.NUXT_ENV_API_URL}/users/payments/payouts-form-data`,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      const data = JSON.parse(response.data)
      commit('SET_PAYOUT_FORM_DATA', data)
      return data
    } catch (error) {
      commit('SET_ERROR', error.message)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async requestSavedAccountPayout({ commit, dispatch }, accountNumber) {
    try {
      commit('SET_LOADING', true)
      const response = await this.$axios.post(
        `${process.env.NUXT_ENV_API_URL}/users/payments/payouts-saved-account`,
        JSON.stringify({ accountNumber }),
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      // Parse the response data
      const data = JSON.parse(response.data)

      // Refresh payouts after successful request
      await dispatch('fetchPayouts')

      // Return the response for the component to show a toast
      return {
        success: true,
        message: data.message || 'Payout request submitted successfully',
      }
    } catch (error) {
      // Get detailed error information
      let errorMessage = 'Failed to submit payout request'

      if (error.response) {
        // Try to extract a more specific error message
        if (error.response.data) {
          if (typeof error.response.data === 'string') {
            try {
              const parsedError = JSON.parse(error.response.data)
              errorMessage = parsedError.message || errorMessage
            } catch (e) {
              // If parsing fails, use the raw response data if it's a string
              errorMessage = error.response.data || errorMessage
            }
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message
          }
        }

        // Add status code to error message for debugging
        errorMessage += ` (Status: ${error.response.status})`
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'No response received from server'
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message || errorMessage
      }

      commit('SET_ERROR', errorMessage)

      // Return error for the component to show a toast
      return {
        success: false,
        message: errorMessage,
      }
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async requestWiseTransfer({ commit, dispatch }, formData) {
    try {
      commit('SET_LOADING', true)
      // Make the API request with JSON data
      await this.$axios.post(
        `${process.env.NUXT_ENV_API_URL}/users/payments/payouts-transfer-wise`,
        formData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      // Refresh payouts after successful request
      await dispatch('fetchPayouts')

      // Return success for the component to show a toast
      return {
        success: true,
        message: 'Payout request submitted successfully',
      }
    } catch (error) {
      // Get detailed error information
      let errorMessage = 'Failed to submit payout request'

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx

        // Try to extract a more specific error message
        if (error.response.data) {
          if (typeof error.response.data === 'string') {
            try {
              const parsedError = JSON.parse(error.response.data)
              errorMessage = parsedError.message || errorMessage
            } catch (e) {
              // If parsing fails, use the raw response data if it's a string
              errorMessage = error.response.data || errorMessage
            }
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message
          }
        }

        // Add status code to error message for debugging
        errorMessage += ` (Status: ${error.response.status})`
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'No response received from server'
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message || errorMessage
      }

      commit('SET_ERROR', errorMessage)

      // Return error for the component to show a toast
      return {
        success: false,
        message: errorMessage,
      }
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async requestBankPayout({ commit, dispatch }, formData) {
    try {
      commit('SET_LOADING', true)

      // Map the form data to the required API format
      const apiData = {
        accountOwnerName: formData.accountOwnerName || '',
        email: formData.email || '',
        phoneNumber: formData.phoneNumber || '',
        iban: formData.iban || '', // Updated field name
        bic: formData.bic || '', // Updated field name
        addressLine1: formData.addressLine1 || '',
        addressLine2: formData.addressLine2 || '',
        city: formData.city || '',
        region: formData.region || '',
        postalCode: formData.postalCode || '',
        country: formData.country || '',
        typeBank: formData.typeBank || 'rev', // Use provided typeBank or default to 'rev'
        saveThisAccount: formData.saveThisAccount || false, // Updated field name
        currency: formData.currency || null,
      }
      // Make the API request with JSON data
      await this.$axios.post(
        `${process.env.NUXT_ENV_API_URL}/users/payments/payouts`,
        apiData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      // Refresh payouts after successful request
      await dispatch('fetchPayouts')

      // Return success for the component to show a toast
      return {
        success: true,
        message: 'Payment confirmed successfully',
      }
    } catch (error) {
      // Get detailed error information
      let errorMessage = 'Payment not confirmed'

      if (error.response) {
        // Try to extract a more specific error message
        if (error.response.data) {
          if (typeof error.response.data === 'string') {
            try {
              const parsedError = JSON.parse(error.response.data)
              errorMessage = parsedError.message || errorMessage
            } catch (e) {
              // If parsing fails, use the raw response data if it's a string
              errorMessage = error.response.data || errorMessage
            }
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message
          }
        }

        // Add status code to error message for debugging
        errorMessage += ` (Status: ${error.response.status})`
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'No response received from server'
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message || errorMessage
      }

      commit('SET_ERROR', errorMessage)

      // Return error for the component to show a toast
      return {
        success: false,
        message: errorMessage,
      }
    } finally {
      commit('SET_LOADING', false)
    }
  },

  /**
   * Fetch user data directly from API
   * This is used to get the most up-to-date user information
   * Returns a promise that resolves with the user data
   */
  fetchUserData() {
    // Get the API URL for fetching user data
    const url = `${process.env.NUXT_ENV_API_URL}/user/get-logged-user`

    // Make the API call
    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        return data
      })
      .catch((error) => {
        console.error('Error fetching user data from API:', error)
        return null
      })
  },

  /**
   * Track a paid trial purchase and send data to GTM
   * This is called after a successful payment when redirecting to the lessons page
   */
  async trackPaidTrialPurchase({ dispatch, rootState }, paymentData) {
    try {
      // Import the hash utility
      const { hashUserData } = require('@/utils/hash')

      // First try to get user data from the API for the most up-to-date information
      let userData = await dispatch('fetchUserData')

      // If API call fails, fall back to store state
      if (!userData || !userData.email) {
        userData = rootState.user.item || {}
      }

      // Get tidio data from the store
      const tidioData = rootState.user.tidioData || {}

      // Get user email (prefer tidioData.email if available) and name
      const userEmail = tidioData.email || userData.email || ''
      const userName = `${userData.firstName || ''} ${
        userData.lastName || ''
      }`.trim()

      const hashedEmail = hashUserData(userEmail)
      const hashedName = hashUserData(userName)

      if (window.dataLayer) {
        window.dataLayer.push({
          event: 'purchase_paid_trial',
          ecommerce: {
            transaction_id: paymentData.transactionId || `tr-${Date.now()}`,
            value: paymentData.amount || 0,
            currency: paymentData.currency || 'EUR',
            items: [
              {
                item_id: paymentData.lessonId || 'paid_trial',
                item_name: 'Paid Trial Lesson',
                price: paymentData.amount || 0,
                quantity: 1,
                user_name: hashedName,
                email_id: hashedEmail,
              },
            ],
          },
        })
      }

      // Store the payment data in localStorage to persist across page reloads
      // This will be used by the lessons page to display a confirmation message
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(
          'recent_paid_trial',
          JSON.stringify({
            timestamp: Date.now(),
            ...paymentData,
          })
        )
      }

      return true
    } catch (error) {
      console.error('Error in trackPaidTrialPurchase:', error)
      // Silent fail
      return false
    }
  },
}

export const getters = {
  payments: (state) => (type) => {
    return type === 'payouts' ? state.payouts || [] : state.lessons || []
  },
  totalPages: (state) => (type) => {
    // Calculate total pages based on the type (payouts or lessons)
    const totalItems =
      type === 'payouts' ? state.totalPayouts : state.totalLessons

    return Math.ceil(totalItems / state.itemsPerPage) || 1 // Ensure at least 1 page
  },
  totalItems: (state) => (type) => {
    // Return total items based on the type
    return type === 'payouts' ? state.totalPayouts : state.totalLessons
  },
  currentPage: (state) => state.currentPage,
  earningsBalance: (state) =>
    state.earningsBalance || {
      balance: '0',
      payouts: '0',
      fees: '0',
      futureIncome: '0',
      currencyId: '2',
    },
  payoutFormData: (state) => state.payoutFormData,
  savedBankAccounts: (state) => state.payoutFormData.saveUserBankAccounts || [],
}

// Add a currency formatter helper
export const currencyFormatter = (amount, currency) => {
  const currencySymbols = {
    EUR: '€',
    USD: '$',
    GBP: '£',
    PLN: 'zł',
    CAD: 'C$',
    AUD: 'A$',
  }

  // Convert amount to a number and format with exactly 2 decimal places
  const formattedAmount = Number(amount).toFixed(2)

  // Get the currency symbol
  const symbol = currencySymbols[currency] || currency

  // Format based on currency symbol
  if (symbol === 'zł' || symbol === 'PLN') {
    return `${formattedAmount} ${symbol === 'PLN' ? 'zł' : symbol}`
  } else {
    return `${symbol}${formattedAmount}`
  }
}

export const hashSHA256 = async (text) => {
  if (!text) return ''
  const encoder = new TextEncoder()
  const data = encoder.encode(text)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
}
