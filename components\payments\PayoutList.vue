<template>
  <div class="payout-list">
    <div
      v-for="payout in payouts"
      :key="payout.id"
      class="payout-item pa-4 mb-3"
    >
      <div class="d-flex justify-space-between align-center">
        <div
          class="payout-date"
          :style="{ background: 'linear-gradient(to right, #95ce32, #80b622)' }"
        >
          <div class="white--text font-weight-medium">{{ payout.date }}</div>
          <div class="white--text caption">{{ payout.time }}</div>
        </div>
        <div class="payout-details flex-grow-1 ml-4">
          <div class="d-flex justify-space-between">
            <div class="text-h6">€{{ payout.amount }}</div>
            <div class="caption grey--text">
              {{ $t('method') }}: {{ payout.method }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PayoutList',
  props: {
    payouts: {
      type: Array,
      default: () => [],
    },
  },
}
</script>

<style lang="scss" scoped>
.payout-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .payout-date {
    padding: 8px 16px;
    border-radius: 4px;
    min-width: 100px;
    text-align: center;
  }
}
</style>
