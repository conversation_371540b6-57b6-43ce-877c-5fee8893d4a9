<template>
  <div class="lesson-list">
    <div
      v-for="lesson in lessons"
      :key="lesson.id"
      class="lesson-item pa-4 mb-3"
    >
      <div class="d-flex justify-space-between align-center">
        <div
          class="lesson-date"
          :style="{ background: 'linear-gradient(to right, #95ce32, #80b622)' }"
        >
          <div class="white--text font-weight-medium">{{ lesson.date }}</div>
          <div class="white--text caption">{{ lesson.time }}</div>
        </div>
        <div class="lesson-details flex-grow-1 ml-4">
          <div class="font-weight-medium">{{ lesson.studentName }}</div>
          <div class="caption grey--text">
            {{ $t('lesson') }}: {{ lesson.type }}
            <span class="mx-2">|</span>
            {{ $t('finished') }}: {{ lesson.finishedAt }}
            <span class="mx-2">|</span>
            {{ $t('lesson_no') }}: {{ lesson.lessonNo }}
            <span class="mx-2">|</span>
            {{ $t('value') }}: €{{ lesson.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LessonList',
  props: {
    lessons: {
      type: Array,
      default: () => [],
    },
  },
}
</script>

<style lang="scss" scoped>
.lesson-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .lesson-date {
    padding: 8px 16px;
    border-radius: 4px;
    min-width: 100px;
    text-align: center;
  }
}
</style>
