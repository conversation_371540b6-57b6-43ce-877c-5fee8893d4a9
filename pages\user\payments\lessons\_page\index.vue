<template>
  <payments-page :type="'lessons'" :page="page"></payments-page>
</template>

<script>
import PaymentsPage from '~/components/payments/PaymentsPage'

export default {
  name: 'LessonsPageWithPagination',
  components: { PaymentsPage },
  middleware: ['authenticated', 'paymentsPageClass'],

  async asyncData({ params, store, query }) {
    const page = parseInt(params.page) || 1
    const searchQuery = query?.search

    await store.dispatch('payments/fetchLessons', {
      page,
      itemsPerPage: 5, // Set to 5 items per page
    })

    // Set current page in store
    store.commit('payments/SET_CURRENT_PAGE', page)

    return {
      page,
      type: 'lessons',
      searchQuery,
    }
  },

  watchQuery: ['page', 'search'],
}
</script>
